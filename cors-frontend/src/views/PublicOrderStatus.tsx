'use client';
import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Alert, CircularProgress, Paper } from '@mui/material';
import {
  OrderDetailsCard,
  LineItemsTable,
  NewImageRequestView,
  CustomerContactView,
  CustomerApprovalView,
} from '@/components/PublicOrderStatus';
import { PublicOrderStatus } from '@/types/public-order-status.types';

interface PublicOrderStatusPageProps {
  initialOrderData?: PublicOrderStatus | null;
  initialError?: string | null;
  shopifyOrderNumber?: string;
  customerEmail?: string;
  formType?: 'newImageRequest' | 'customerContactNeeded' | 'customerApproval';
}

const PublicOrderStatusPage: React.FC<PublicOrderStatusPageProps> = ({
  initialOrderData,
  initialError,
  formType,
}) => {
  const [orderData, setOrderData] = useState<PublicOrderStatus | null>(initialOrderData || null);
  const [loading] = useState(false);
  const [error, setError] = useState<string | null>(initialError || null);

  // Set initial state based on server-side data
  useEffect(() => {
    if (initialOrderData) {
      setOrderData(initialOrderData);
    }
    if (initialError) {
      setError(initialError);
    }
  }, [initialOrderData, initialError]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          Order Status
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Track your order progress and request updates
        </Typography>
      </Box>

      {/* Professional Loading State */}
      {loading && (
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          py={6}
          gap={2}
        >
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" color="text.secondary">
            Loading order details...
          </Typography>
        </Box>
      )}

      {/* Error State */}
      {error && !loading && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Order Data */}
      {orderData && !loading && (
        <Box>
          {formType === 'newImageRequest' ? (
            <NewImageRequestView orderData={orderData} formType={formType} />
          ) : formType === 'customerContactNeeded' ? (
            <CustomerContactView orderData={orderData} />
          ) : formType === 'customerApproval' ? (
            <CustomerApprovalView orderData={orderData} />
          ) : (
            <>
              <OrderDetailsCard orderData={orderData} />
              <LineItemsTable
                lineItems={orderData.lineItems}
                orderNumber={orderData.shopifyOrderNumber}
                formType={formType}
              />
            </>
          )}
        </Box>
      )}

      {/* Help Section */}
      <Paper elevation={1} sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Need Help?
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          If you're having trouble with your order or need assistance, please contact our customer
          support team.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • Make sure you're providing the correct order information
        </Typography>
      </Paper>
    </Container>
  );
};

export default PublicOrderStatusPage;
